<template>
	<view class="page">
		<view class="header">
			<image :src="serviceInfo.cover" mode="scaleToFill"></image>
		</view>
		<view class="content">
			<view class="card">
				<view class="top">
					<view class="title">{{ serviceInfo.title }}</view>
					<view class="price" v-if="serviceInfo.servicePriceType != 1 && type == 0">￥{{ serviceInfo.price }}</view>
				</view>
				<view class="bottom">
					<view class="left">已选：</view>
					<view class="right">
						<view class="" v-if="serviceInfo.servicePriceType == 1">
							{{ yikoujiaprice }}
						</view>
						<view class="tag" v-for="(item, index) in chooseArr" :key="index">{{ item.name }}</view>
					</view>
				</view>
			</view>
			<view class="chol" v-for="(item, index) in list" :key="item.id">
				<view class="choose">
					<view class="title"><span v-if="item.isRequired == 1">*</span>{{ item.problemDesc }}</view>
					<view class="desc">{{ item.problemContent }}</view>
					<view class="cho_box">
						<view class="box_item" v-for="(newItem, newIndex) in item.options" :key="newIndex"
							:style="newItem.choose ? 'border:2rpx solid #2E80FE;color: #2E80FE;' : ''"
							@click="chooseOne(index, newIndex, item.inputType)">
							{{ newItem.name }}
							<view class="ok" :style="newItem.choose ? '' : 'display:none;'">
								<uni-icons type="checkmarkempty" size="8" color="#fff"></uni-icons>
							</view>
						</view>
					</view>
				</view>
				<view class="fg"></view>
			</view>

			<view class="chol" v-for="(item, index) in list2" :key="item.id">
				<view class="choose">
					<view class="title"><span v-if="item.isRequired == 1">*</span>{{ item.problemDesc }}</view>
					<view class="desc">{{ item.problemContent }}</view>
					<view class="input-container" :id="'input-container-' + index">
						<input type="text" v-model="form.data[index + list.length].val"
							:placeholder="'请输入' + item.problemDesc" @focus="handleInputFocus(index)"
							@blur="handleInputBlur" @input="handleInput" class="form-input" cursor-spacing="10"
							confirm-type="done" :adjust-position="false" :auto-height="false" />
					</view>
				</view>
				<view class="fg"></view>
			</view>

			<view class="chol" v-for="(item, index) in list3" :key="item.id">
				<view class="choose">
					<view class="title"><span v-if="item.isRequired == 1">*</span>{{ item.problemDesc }}</view>
					<view class="desc up">{{ item.problemContent }}</view>
					<upload @upload="imgUpload" @del="imgUpload"
						:imagelist="form.data[form.data.findIndex(e => e.serviceId == item.id)].val"
						:imgtype="form.data.findIndex(e => e.serviceId == item.id)" text="上传图片" :imgsize="3">
					</upload>
				</view>
				<view class="fg"></view>
			</view>
			<view style="height: 300rpx;"></view>
		</view>
		<view class="cart-modal" v-if="showOrderModal" @click="closeOrderModal">
			<view class="cart-modal-content" @click.stop>
				<view class="modal-header">
					<view class="modal-service-info">
						<image :src="serviceInfo.cover" mode="aspectFill" class="modal-service-image"></image>
						<view class="modal-service-details">
							<view class="modal-service-title">{{ serviceInfo.title }}</view>
							<view class="modal-service-price" v-if="serviceInfo.servicePriceType != 1 && type == 0">
								￥{{ serviceInfo.price }}
							</view>

							<view class="modal-quantity-section">
								<view class="modal-quantity-title">数量</view>
								<view class="modal-quantity-control">
									<view class="quantity-btn" @click="decreaseQuantity">-</view>
									<view class="quantity-input">{{ orderQuantity }}</view>
									<view class="quantity-btn" @click="increaseQuantity">+</view>
								</view>
							</view>

							<view class="modal-urgent-section">
								<view class="modal-urgent-checkbox" @click="toggleUrgent">
									<view class="checkbox-icon" :class="{ 'checked': isUrgent }">
										<uni-icons v-if="isUrgent" type="checkmarkempty" size="12" color="#fff">
										</uni-icons>
									</view>
									<text class="checkbox-label">是否加急</text>
								</view>
							</view>

						</view>
					</view>
					<view class="modal-close" @click="closeOrderModal">
						<uni-icons type="clear" size="24" color="#999"></uni-icons>
					</view>
				</view>

				<scroll-view class="modal-scroll-content" scroll-y="true">
					<view class="modal-address-section" @click="goToAddress">
						<view class="modal-section-title">
							<image src="../static/images/position.png" mode="" class="section-icon"></image>
							<text>服务地址</text>
						</view>
						<view class="modal-address-content">
							<view class="address-text">{{ mrAddress.address || '请选择服务地址' }}</view>
							<view class="address-detail" v-if="mrAddress.address">{{ mrAddress.userName }}
								{{ mrAddress.mobile }}</view>
						</view>
						<uni-icons name="arrow-right" size="16" color="#999"></uni-icons>
					</view>

					<view class="modal-time-section" @click="showTimeModal = true">
						<view class="modal-section-title">
							<image src="../static/images/clock.png" mode="" class="section-icon"></image>
							<text>预约时间</text>
						</view>
						<view class="modal-time-content">
							<text>{{ conDate + (conTime ? ' ' + conTime : '') }}</text>
						</view>
						<uni-icons name="arrow-right" size="16" color="#999"></uni-icons>
					</view>

					<view class="modal-notes-section">
						<view class="modal-notes-title">服务备注</view>
						<textarea v-model="notes" placeholder="想要额外嘱咐工作人员的可以备注哦~" class="modal-notes-textarea">
						</textarea>
					</view>
				</scroll-view>

				<view class="modal-footer">
					<view class="modal-footer-buttons">
						<view class="modal-order-btn" :class="{ 'submitting': isSubmittingOrder }"
							@click="confirmOrder">
							{{ isSubmittingOrder ? '提交中...' : '立即下单' }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="time-modal" v-if="showTimeModal" @click="closeTimeModal">
			<view class="time-modal-content" @click.stop>
				<view class="time-modal-header">
					<view class="time-modal-title">请选择时间</view>
					<view class="time-modal-close" @click="closeTimeModal">
						<uni-icons type="clear" size="24" color="#999"></uni-icons>
					</view>
				</view>

				<view class="time-date-section">
					<view class="time-date-item" v-for="(item, index) in dateArr" :key="index"
						:class="{ 'active': currentDate === index }" @click="selectDate(item, index)">
						<view class="date-str">{{ item.str }}</view>
						<view class="date-num">{{ item.date }}</view>
					</view>
				</view>

				<scroll-view class="time-slots-section" scroll-y="true">
					<view class="time-slots-grid">
						<view class="time-slot-column">
							<view class="time-slot-item" v-for="(item, index) in displayedTimeArr.slice(0, 6)"
								:key="index" :class="{
									'active': currentTime === index,
									'disabled': item.disabled
								}" @click="selectTime(item, index)">
								{{ item.time }}
							</view>
						</view>
						<view class="time-slot-column">
							<view class="time-slot-item" v-for="(item, index) in displayedTimeArr.slice(6)" :key="index"
								:class="{
									'active': currentTime === index + 6,
									'disabled': item.disabled
								}" @click="selectTime(item, index + 6)">
								{{ item.time }}
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="time-modal-footer">
					<view class="time-confirm-btn" @click="confirmTime">确定预约时间</view>
				</view>
			</view>
		</view>

		<view class="footer" :class="{ 'footer-single-button': type == 0 }" :style="footerStyle">
			<!-- 	<view class="footer-item footer-service" @click="contactService">
				<view class="footer-icon">
					<uni-icons type="chatbubble" size="20" color="#666"></uni-icons>
				</view>
				<view class="footer-text">客服</view>
			</view>

			<view class="footer-item footer-cart" @click="goToCart">
				<view class="footer-icon">
					<uni-icons type="cart" size="20" color="#666"></uni-icons>
				</view>
				<view class="footer-text">购物车</view>
			</view> -->

			<view v-if="type != 0" class="footer-item footer-add-cart" :class="{ 'submitting': isAddingToCart }"
				@click="confirmAddToCart">
				{{ isAddingToCart ? '加入中...' : '加入购物车' }}
			</view>

			<view class="footer-item footer-order" :class="{ 'submitting': isSubmittingOrder }"
				@click="handleOrderClick">
				{{ isSubmittingOrder ? '提交中...' : '立即下单' }}
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			id: '',
			type: '',
			chooseArr: [],
			list: [], // 单选多选框
			list2: [], // 输入框
			list3: [], // 上传图片
			serviceInfo: {},
			form: {
				data: [],
				id: ''
			},
			tmplIds: [
				'',
				'',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			btArr: [], // 必填项
			focusedInputIndex: -1,
			keyboardHeight: 0,
			yikoujiaprice: '',
			windowHeight: 0,
			isKeyboardShow: false,
			systemInfo: {},
			scrollTimer: null,
			isAddingToCart: false, // 加入购物车提交状态
			isSubmittingOrder: false, // 立即下单提交状态
			showOrderModal: false, // 控制下单弹窗显示

			// 新增：下单相关数据
			orderQuantity: 1, // 下单数量
			mrAddress: {}, // 服务地址
			isAddressManuallySelected: false, // 标记用户是否手动选择了地址
			showTimeModal: false, // 时间选择弹窗
			currentDate: 0, // 当前选择的日期索引
			currentTime: -1, // 当前选择的时间索引
			conDate: '选择可上门时间', // 显示的日期
			conTime: '', // 显示的时间
			dateArr: [], // 日期数组
			// Base time array - always contains all time slots
			baseTimeArr: [
				{ disabled: false, time: '00:00-02:00', time1: '00:00:00', time2: '02:00:00' },
				{ disabled: false, time: '02:00-04:00', time1: '02:00:00', time2: '04:00:00' },
				{ disabled: false, time: '04:00-06:00', time1: '04:00:00', time2: '06:00:00' },
				{ disabled: false, time: '06:00-08:00', time1: '06:00:00', time2: '08:00:00' },
				{ disabled: false, time: '08:00-10:00', time1: '08:00:00', time2: '10:00:00' },
				{ disabled: false, time: '10:00-12:00', time1: '10:00:00', time2: '12:00:00' },
				{ disabled: false, time: '12:00-14:00', time1: '12:00:00', time2: '14:00:00' },
				{ disabled: false, time: '14:00-16:00', time1: '14:00:00', time2: '16:00:00' },
				{ disabled: false, time: '16:00-18:00', time1: '16:00:00', time2: '18:00:00' },
				{ disabled: false, time: '18:00-20:00', time1: '18:00:00', time2: '20:00:00' },
				{ disabled: false, time: '20:00-22:00', time1: '20:00:00', time2: '22:00:00' },
				{ disabled: false, time: '22:00-24:00', time1: '22:00:00', time2: '24:00:00' }
			],
			displayedTimeArr: [], // Time array adjusted based on selected date (today vs future)
			week: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
			isUrgent: false, // 是否加急
			notes: '' // 服务备注
		}
	},

	computed: {
		footerStyle() {
			return {
				bottom: this.isKeyboardShow ? this.keyboardHeight + 'px' : '0px'
			}
		},

		// 计算总价
		totalPrice() {
			if (this.serviceInfo.servicePriceType === 1) {
				return '0.00'; // 报价类型显示0元
			}
			const basePrice = this.serviceInfo.price || 0;
			return (basePrice * this.orderQuantity).toFixed(2);
		}
	},

	methods: {
		dingyue() {
			console.log('dingyue called');
			const allTmplIds = this.tmplIds;
			if (allTmplIds.length < 3) {
				console.error("Not enough template IDs available:", allTmplIds);
				// uni.showToast({
				// icon: 'none',
				// title: '模板ID不足'
				// });
				return;
			}
			const shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());
			const selectedTmplIds = shuffled.slice(0, 3);
			console.log("Selected template IDs:", selectedTmplIds);
			const templateData = selectedTmplIds.map((id, index) => ({
				templateId: id,
				templateCategoryId: index === 0 ? 10 : 5
			}));
			uni.requestSubscribeMessage({
				tmplIds: selectedTmplIds,
				success: (res) => {
					console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
					// Check if any of the template IDs were rejected
					const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
					const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
					if (hasRejection && !hasShownModal) {
						uni.showModal({
							title: '提示',
							content: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。',
							cancelText: '取消',
							confirmText: '去开启',
							confirmColor: '#007AFF',
							success: (modalRes) => {
								uni.setStorageSync('hasShownSubscriptionModal', true);
								if (modalRes.confirm) {
									uni.openSetting({
										withSubscriptions: true
									});
								} else if (modalRes.cancel) {
									uni.setStorageSync('hasCanceledSubscription', true);
								}
							}
						});
					}
					this.templateCategoryIds = [];
					selectedTmplIds.forEach((templId, index) => {
						console.log(`Template ${templId} status: ${res[templId]}`);
						if (res[templId] === 'accept') {
							const templateCategoryId = templateData[index].templateCategoryId;
							if (templateCategoryId === 10) {
								for (let i = 0; i < 15; i++) {
									this.templateCategoryIds.push(templateCategoryId);
								}
							} else {
								this.templateCategoryIds.push(templateCategoryId);
							}
							console.log('Accepted message push for template:', templId);
						}
					});
					console.log('Updated templateCategoryIds:', this.templateCategoryIds);
				},
				fail: (err) => {
					console.error('requestSubscribeMessage failed:', err);
				}
			});
		},
		handleInputFocus(index) {
			console.log('输入框获得焦点:', index);
			this.focusedInputIndex = index;
			this.isKeyboardShow = true;

			// 清除之前的定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
			}

			// 多次尝试滚动，确保定位准确
			this.scrollToInput(index);

			this.scrollTimer = setTimeout(() => {
				this.scrollToInput(index);
			}, 200);

			this.scrollTimer = setTimeout(() => {
				this.scrollToInput(index);
			}, 400);

			this.scrollTimer = setTimeout(() => {
				this.scrollToInput(index);
			}, 600);
		},

		handleInputBlur() {
			console.log('输入框失去焦点');
			this.focusedInputIndex = -1;
			this.isKeyboardShow = false;
			this.keyboardHeight = 0;

			// 清除定时器
			if (this.scrollTimer) {
				clearTimeout(this.scrollTimer);
				this.scrollTimer = null;
			}
		},

		handleInput(e) {
			console.log('输入内容:', e.detail.value);
		},

		scrollToInput(index) {
			const query = uni.createSelectorQuery().in(this);

			// 同时获取输入框和页面信息
			query.select(`#input-container-${index}`).boundingClientRect();
			query.selectViewport().scrollOffset();

			query.exec((res) => {
				if (res && res[0] && res[1]) {
					const inputRect = res[0];
					const pageScrollInfo = res[1];

					console.log('输入框位置信息:', {
						inputRect,
						pageScrollInfo,
						systemInfo: this.systemInfo
					});

					// 输入框距离页面顶部的绝对位置
					const inputAbsoluteTop = inputRect.top + pageScrollInfo.scrollTop;

					// 获取当前系统信息
					const systemInfo = uni.getSystemInfoSync();
					const windowHeight = systemInfo.windowHeight;
					const statusBarHeight = systemInfo.statusBarHeight || 0;

					// 预估键盘高度（一般占屏幕高度的40-50%）
					let keyboardHeight = this.keyboardHeight;
					if (!keyboardHeight || keyboardHeight < 100) {
						keyboardHeight = windowHeight * 0.45; // 预估键盘高度
					}

					// 计算可视区域高度（窗口高度 - 键盘高度）
					const visibleHeight = windowHeight - keyboardHeight;

					// 计算安全位置：让输入框显示在可视区域的上1/3处
					const safePosition = visibleHeight * 0.3;

					// 计算目标滚动位置
					const targetScrollTop = inputAbsoluteTop - safePosition - statusBarHeight;

					console.log('滚动计算详情:', {
						inputAbsoluteTop,
						windowHeight,
						keyboardHeight,
						visibleHeight,
						safePosition,
						targetScrollTop,
						currentScrollTop: pageScrollInfo.scrollTop
					});

					// 只有当需要滚动的距离超过50px时才执行滚动
					if (targetScrollTop > 0 && Math.abs(targetScrollTop - pageScrollInfo.scrollTop) > 50) {
						uni.pageScrollTo({
							scrollTop: Math.max(0, targetScrollTop), // 确保不会滚动到负数位置
							duration: 300,
							success: () => {
								console.log('页面滚动成功到:', targetScrollTop);
							},
							fail: (err) => {
								console.error('页面滚动失败:', err);
							}
						});
					} else {
						console.log('无需滚动或滚动距离太小');
					}
				} else {
					console.error('获取元素位置信息失败:', res);
				}
			});
		},

		imgUpload(e) {
			let {
				imagelist,
				imgtype
			} = e;
			let newFormData = [...this.form.data];
			newFormData[imgtype] = {
				...newFormData[imgtype],
				val: [...imagelist]
			};
			this.$set(this.form, 'data', newFormData);
		},

		async getpzinfo() {
			await this.$api.service.getPz({
				id: this.id,
				type: this.type
			}).then(ress => {
				let res = ress.data
				res.forEach(item => {
					if (item.isRequired == 1) {
						this.btArr.push(item.id)
					}
					item.options = JSON.parse(item.options)
					item.options = item.options.map(e => {
						return {
							serviceId: item.id,
							name: e,
							choose: false
						}
					})
				})
				this.list = res.filter(item => item.inputType == 3 || item.inputType == 4)
				this.list.forEach((newItem, newIndex) => {
					this.form.data.push({
						"serviceId": newItem.id,
						"settingId": this.id,
						"val": []
					})
				})
				this.list2 = res.filter(item => item.inputType == 1)
				this.list2.forEach((newItem, newindex) => {
					this.form.data.push({
						"serviceId": newItem.id,
						"settingId": this.id,
						"val": ''
					})
				})
				console.log(this.list2)
				this.list3 = res.filter(item => item.inputType == 2)
				this.list3.forEach((newItem, newindex) => {
					this.form.data.push({
						"serviceId": newItem.id,
						"settingId": this.id,
						"val": []
					})
				})
			})
		},

		submit() {
			this.showOrderModal = true;
		},

		chooseOne(i, j, inputType) {
			// Single choice logic
			if (inputType == 3) {
				this.list[i].options.forEach((item, index) => {
					item.choose = (index === j); // Only the selected one is true
				});
			} else if (inputType == 4) {
				// Multiple choice logic
				this.list[i].options[j].choose = !this.list[i].options[j].choose;
			}

			// Update chooseArr based on current selections across all list items
			this.chooseArr = [];
			this.list.forEach(item => {
				item.options.forEach(tem => {
					if (tem.choose) {
						this.chooseArr.push(tem);
					}
				});
			});
		},

		async getInfo() {
			await this.$api.service.getserviceInfo(this.id).then(res => {
				this.serviceInfo = res.data
				// Only set yikoujiaprice if servicePriceType is 1, otherwise it will be calculated
				if (this.serviceInfo.servicePriceType === 1) {
					this.yikoujiaprice = res.data.price;
				}
			})
		},

		// 联系客服
		contactService() {
			uni.showActionSheet({
				itemList: ['在线客服', '电话客服'],
				success: (res) => {
					if (res.tapIndex === 0) {
						// 在线客服 - 可以跳转到客服页面或打开客服聊天
						uni.navigateTo({
							url: '/pages/service/online-service'
						});
					} else if (res.tapIndex === 1) {
						// 电话客服
						uni.makePhoneCall({
							phoneNumber: '************' // 替换为实际客服电话
						});
					}
				}
			});
		},

		// 跳转购物车
		goToCart() {
			uni.switchTab({
				url: '/pages/cart/cart'
			});
		},

		// 确认加入购物车 (直接执行逻辑，不显示弹窗)
		confirmAddToCart() {
			// 防止重复提交
			if (this.isAddingToCart) {
				return;
			}

			// 设置提交状态
			this.isAddingToCart = true;

			// 先验证并处理表单数据
			let copy_form = JSON.parse(JSON.stringify(this.form))
			this.chooseArr.forEach(item => {
				const targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);
				if (targetIndex !== -1) {
					if (Array.isArray(copy_form.data[targetIndex].val)) {
						copy_form.data[targetIndex].val.push(item.name);
					} else {
						// If val is not an array, initialize it as an array
						copy_form.data[targetIndex].val = [item.name];
					}
				}
			})

			let isValid = true
			copy_form.data.forEach(item => {
				let index = this.btArr.findIndex(e => e == item.serviceId)
				if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
					uni.showToast({
						icon: 'none',
						title: '请填写完整后提交',
						duration: 1500
					})
					isValid = false
					return
				}
				// Fill empty val with "无"
				if (item.val == '' || (Array.isArray(item.val) && item.val.length === 0)) {
					item.val = "无"
				}
			})

			if (!isValid) {
				this.isAddingToCart = false; // 验证失败时重置状态
				return;
			}

			// 处理数据格式
			copy_form.data = copy_form.data.map(item => ({
				...item,
				serviceId: item.settingId,
				settingId: item.serviceId
			}))

			copy_form.data.forEach(item => {
				let type = typeof item.val
				if (type != 'string') {
					if (Array.isArray(item.val) && item.val.length > 0 && typeof item.val[0] != 'string') {
						item.val = item.val.map(e => {
							return e.path
						}).join(',')
					} else if (Array.isArray(item.val)) {
						item.val = item.val.join(',')
					}
				}
			})

			// First step: Submit configuration information
			this.$api.service.postPz(copy_form).then(res => {
				if (res.code === "200") {
					// Second step: Add to cart after configuration is successfully submitted
					this.$api.service.addtocar({
						serviceId: this.id,
						num: this.orderQuantity // Use quantity from the modal
					}).then(cartRes => {
						uni.showToast({
							icon: 'success',
							title: '加入成功'
						});
						setTimeout(() => {
							this.isAddingToCart = false;

							uni.redirectTo({
								url: '../pages/order'
							});
						}, 1000);
					}).catch(cartErr => {
						// Failed to add to cart
						this.isAddingToCart = false;
						uni.showToast({
							icon: 'error',
							title: '加入购物车失败，请重试',
							duration: 2000
						});
						console.error('Add to cart failed:', cartErr);
					});
				} else {
					// Configuration submission failed
					this.isAddingToCart = false;
					uni.showToast({
						icon: 'error',
						title: '请重新尝试',
						duration: 1000
					});
				}
			}).catch(err => {
				// Configuration submission network error
				this.isAddingToCart = false;
				uni.showToast({
					icon: 'error',
					title: '网络错误，请重试',
					duration: 1000
				});
				console.error('Submit config failed:', err);
			});
		},

		// 关闭下单弹窗
		closeOrderModal() {
			this.showOrderModal = false;
		},

		// 处理立即下单点击事件 - 先验证表单完整性
		handleOrderClick() {
			// 防止重复提交
			if (this.isSubmittingOrder) {
				return;
			}

			// 验证表单完整性（类似加入购物车的验证逻辑）
			let copy_form = JSON.parse(JSON.stringify(this.form))
			this.chooseArr.forEach(item => {
				const targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);
				if (targetIndex !== -1) {
					if (Array.isArray(copy_form.data[targetIndex].val)) {
						copy_form.data[targetIndex].val.push(item.name);
					} else {
						// If val is not an array, initialize it as an array
						copy_form.data[targetIndex].val = [item.name];
					}
				}
			})

			let isValid = true
			copy_form.data.forEach(item => {
				let index = this.btArr.findIndex(e => e == item.serviceId)
				if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
					uni.showToast({
						icon: 'none',
						title: '请填写完整后提交',
						duration: 1500
					})
					isValid = false
					return
				}
			})

			if (!isValid) {
				return;
			}

			// 验证通过，显示下单弹窗
			this.showOrderModal = true;
		},

		// 确认下单
		confirmOrder() {
			// 防止重复提交
			if (this.isSubmittingOrder) {
				return;
			}

			// 验证必填项
			if (this.conDate === '选择可上门时间' || !this.conTime) {
				uni.showToast({
					icon: 'none',
					title: '请选择预约时间',
					duration: 1500
				});
				return;
			}

			if (!this.mrAddress || !this.mrAddress.id) {
				uni.showToast({
					icon: 'none',
					title: '请先选择服务地址',
					duration: 1500
				});
				return;
			}

			this.isSubmittingOrder = true; // 设置提交状态

			let copy_form = JSON.parse(JSON.stringify(this.form))
			this.chooseArr.forEach(item => {
				const targetIndex = copy_form.data.findIndex(e => e.serviceId === item.serviceId);
				if (targetIndex !== -1) {
					if (Array.isArray(copy_form.data[targetIndex].val)) {
						copy_form.data[targetIndex].val.push(item.name);
					} else {
						copy_form.data[targetIndex].val = [item.name];
					}
				}
			})
			let open = true
			copy_form.data.forEach(item => {
				let index = this.btArr.findIndex(e => e == item.serviceId)
				if (index != -1 && (item.val == '' || (Array.isArray(item.val) && item.val.length === 0))) {
					uni.showToast({
						icon: 'none',
						title: '请填写完整后提交',
						duration: 1500
					})
					open = false
					return
				}
				// Fill empty val with "无"
				if (item.val == '' || (Array.isArray(item.val) && item.val.length === 0)) {
					item.val = "无"
				}
			})

			if (!open) {
				this.isSubmittingOrder = false; // 验证失败时重置状态
				return;
			}

			if (open) {
				copy_form.data = copy_form.data.map(item => ({
					...item,
					serviceId: item.settingId,
					settingId: item.serviceId
				}))

				copy_form.data.forEach(item => {
					let type = typeof item.val
					if (type != 'string') {
						if (Array.isArray(item.val) && item.val.length > 0 && typeof item.val[0] != 'string') {
							item.val = item.val.map(e => {
								return e.path
							}).join(',')
						} else if (Array.isArray(item.val)) {
							item.val = item.val.join(',')
						}
					}
				})

				console.log(copy_form)
				// First step: Submit configuration information
				this.$api.service.postPz(copy_form).then(res => {
					if (res.code === "200") {
						// Second step: After configuration is successful, submit the order
						const urgentStatus = this.isUrgent ? 1 : 0;
						const selectedDateObj = this.dateArr[this.currentDate];
						const selectedTimeObj = this.displayedTimeArr[this.currentTime]; // Use displayedTimeArr here

						if (!selectedDateObj || !selectedTimeObj) {
							this.isSubmittingOrder = false;
							uni.showToast({
								icon: 'none',
								title: '请选择正确的日期和时间',
							});
							return;
						}

						// Construct full date-time string
						let dateStr = selectedDateObj.fullDate;
						let startTimeStr = `${dateStr} ${selectedTimeObj.time1}`;
						let endTimeStr = `${dateStr} ${selectedTimeObj.time2}`;

						// Convert to timestamp
						let startTimestamp = new Date(startTimeStr).getTime() / 1000;
						let endTimestamp = new Date(endTimeStr).getTime() / 1000;

						// Validate timestamps
						if (isNaN(startTimestamp) || isNaN(endTimestamp)) {
							this.isSubmittingOrder = false;
							uni.showToast({
								icon: 'none',
								title: '时间格式错误，请重新选择',
							});
							return;
						}

						let subForm = {
							type: this.type,
							pid: uni.getStorageSync('pid') || 0,
							addressId: this.mrAddress.id,
							serviceId: this.id,
							urgent: urgentStatus,
							num: this.orderQuantity,
							startTime: startTimestamp,
							endTime: endTimestamp,
							text: this.notes,
							couponId: '', // Temporarily no coupon support
						};

						console.log('提交订单数据:', subForm);

						// Submit order
						this.$api.service.subOrder(subForm).then(orderRes => {
							if (orderRes.code === '200') {
								uni.showToast({
									icon: 'success',
									title: '下单成功',
									duration: 1500
								});
								setTimeout(() => {
									this.isSubmittingOrder = false;
									this.showOrderModal = false;

									// 根据 type 判断跳转页面
									if (this.type == 0) {
										// type 为 0 时跳转到 Cashier.vue 页面
										const orderId = orderRes.data; // 订单ID
										const price = this.serviceInfo.price *this.orderQuantity; // 价格
										const goodsId = this.id; // 商品ID

										uni.redirectTo({
											url: `/user/Cashier?id=${orderId}&price=${price}&type=${this.type}&goodsId=${goodsId}`
										});
									} else {
										// type 为 1 时跳转到原来的页面
										uni.redirectTo({
											url: '/user/wait_price'
										});
									}
								}, 1500);
							} else {
								this.isSubmittingOrder = false;
								uni.showToast({
									icon: 'none',
									title: orderRes.msg || '下单失败，请重试',
									duration: 2000
								});
							}
						}).catch(orderErr => {
							this.isSubmittingOrder = false;
							uni.showToast({
								icon: 'error',
								title: '下单失败，请重试',
								duration: 2000
							});
							console.error('Submit order failed:', orderErr);
						});
					} else {
						this.isSubmittingOrder = false;
						uni.showToast({
							icon: 'error',
							title: '请重新尝试',
							duration: 1000
						});
					}
				}).catch(err => {
					console.error('提交配置失败:', err);
					this.isSubmittingOrder = false;
					uni.showToast({
						icon: 'error',
						title: '网络错误，请重试',
						duration: 1000
					});
				});
			}
		},

		// 数量增加
		increaseQuantity() {
			this.orderQuantity++;
		},

		// 数量减少
		decreaseQuantity() {
			if (this.orderQuantity > 1) {
				this.orderQuantity--;
			}
		},

		// 切换加急状态
		toggleUrgent() {
			this.isUrgent = !this.isUrgent;
		},

		// 跳转到地址选择页面
		goToAddress() {
			if (this.isSubmittingOrder) return;

			uni.navigateTo({
				url: '../user/address',
				success: () => {
					console.log('Navigation to address page successful');
				},
				fail: (err) => {
					console.error('Navigation failed:', err);
					uni.showToast({
						icon: 'none',
						title: '导航失败，请检查页面路径',
						duration: 2000
					});
				}
			});
		},

		// 关闭时间选择弹窗
		closeTimeModal() {
			this.showTimeModal = false;
		},

		// 选择日期
		selectDate(item, index) {
			this.currentDate = index;
			this.currentTime = -1; // 重置时间选择
			this.conTime = ''; // 重置时间显示
			this.updateTimeAvailability(index);
		},

		// 选择时间
		selectTime(item, index) {
			if (!item || !item.time || item.disabled) {
				uni.showToast({
					icon: 'none',
					title: '该时间段不可选择',
					duration: 1000
				});
				return;
			}
			this.currentTime = index;
		},

		// 确认时间选择
		confirmTime() {
			if (this.currentTime === -1) {
				uni.showToast({
					icon: 'none',
					title: '请选择预约时间',
					duration: 1000
				});
				return;
			}
			const selectedTime = this.displayedTimeArr[this.currentTime]; // Use displayedTimeArr here
			if (selectedTime.disabled) {
				uni.showToast({
					icon: 'none',
					title: '该时间段不可用',
					duration: 1000
				});
				return;
			}
			this.conDate = this.dateArr[this.currentDate].date + '(' + this.dateArr[this.currentDate].str + ')';
			this.conTime = selectedTime.time;
			this.showTimeModal = false;
		},

		// 更新时间段可用性
		updateTimeAvailability(dateIndex) {
			console.log('Updating time availability for dateIndex:', dateIndex);
			const now = new Date();
			const currentHour = now.getHours();
			const currentMinute = now.getMinutes();

			// Reset displayed time array
			this.displayedTimeArr = [];

			if (dateIndex === 0) { // Today
				this.baseTimeArr.forEach(item => {
					if (!item.time1) {
						console.warn(`Invalid time slot at index ${index}:`, item);
						return; // Skip invalid items
					}
					const timeStartHour = parseInt(item.time1.split(':')[0]);
					const timeStartMinute = parseInt(item.time1.split(':')[1]);

					// Check if the time slot has already passed
					if (currentHour > timeStartHour || (currentHour === timeStartHour && currentMinute >= timeStartMinute)) {
						// Past time, do not add to displayedTimeArr
					} else {
						// Future time, add to displayedTimeArr and ensure it's not disabled
						this.displayedTimeArr.push({ ...item, disabled: false });
					}
				});
			} else {
				// Other dates, all time slots are available
				this.displayedTimeArr = this.baseTimeArr.map(item => ({ ...item, disabled: false }));
			}
			console.log('Updated displayedTimeArr:', this.displayedTimeArr);
		},


		// 生成日期数组
		generateDateArray() {
			const now = new Date();
			let currentDate = new Date(now);

			for (let i = 0; i < 4; i++) {
				const month = this.addLeadingZero(currentDate.getMonth() + 1);
				const date = this.addLeadingZero(currentDate.getDate());
				const day = currentDate.getDay();
				const year = currentDate.getFullYear();

				this.dateArr.push({
					str: i === 0 ? '今天' : this.week[day],
					date: month + '-' + date,
					fullDate: `${year}-${month}-${date}` // 添加完整日期格式
				});

				// Move to the next day
				currentDate.setDate(currentDate.getDate() + 1);
			}

			// Initialize today's time availability
			this.updateTimeAvailability(0);
		},

		// 添加前导零
		addLeadingZero(number) {
			return number < 10 ? '0' + number : number;
		},

		// 获取默认地址
		async getDefaultAddress() {
			// 如果用户已经手动选择了地址，不要覆盖
			if (this.isAddressManuallySelected) {
				console.log('用户已手动选择地址，跳过获取默认地址');
				return;
			}

			try {
				let res = await this.$api.service.getaddressDefault();
				console.log('获取默认地址:', res.data);
				// 如果API返回null或undefined，设置空对象避免模板访问错误
				this.mrAddress = res.data || {};
			} catch (err) {
				console.error('Get default address failed:', err);
				// 如果获取默认地址失败，设置空对象避免显示错误
				this.mrAddress = {};
			}
		}
	},

	onLoad(options) {
		this.id = options.id
		this.type = options.type
		this.form.id = options.id
		this.getInfo()
		this.getpzinfo()

		// Initialize order related data
		this.generateDateArray()
		this.getDefaultAddress()

		// Get system info
		uni.getSystemInfo({
			success: (res) => {
				this.systemInfo = res;
				this.windowHeight = res.windowHeight;
				console.log('获取到系统信息:', res);
			}
		});
	},

	// Listen for keyboard height changes
	onKeyboardHeightChange(res) {
		console.log('键盘高度变化事件:', res);
		this.keyboardHeight = res.height;
		this.isKeyboardShow = res.height > 0;

		// When keyboard height changes, if there's a focused input, re-adjust position
		if (this.focusedInputIndex >= 0) {
			setTimeout(() => {
				this.scrollToInput(this.focusedInputIndex);
			}, 50);
		}
	},

	// Reset status when page shows
	onShow() {
		this.focusedInputIndex = -1;
		this.isKeyboardShow = false;
		this.keyboardHeight = 0;
		this.isAddingToCart = false; // Reset add to cart status
		this.isSubmittingOrder = false; // Reset order submission status
		if (this.scrollTimer) {
			clearTimeout(this.scrollTimer);
			this.scrollTimer = null;
		}

		// Listen for address selection callback
		let that = this;
		uni.$once('chooseAddress', function (e) {
			console.log('收到地址选择事件:', e);
			that.mrAddress = e;
			that.isAddressManuallySelected = true; // 标记用户手动选择了地址
		});

		// Only re-fetch default address if user hasn't manually selected an address
		// This prevents overriding user's address selection
		if (!this.isAddressManuallySelected && (!this.mrAddress || !this.mrAddress.id)) {
			this.getDefaultAddress();
		}
	},

	// Clean up when page hides
	onHide() {
		if (this.scrollTimer) {
			clearTimeout(this.scrollTimer);
			this.scrollTimer = null;
		}
	},

	// Clean up when page unloads
	onUnload() {
		if (this.scrollTimer) {
			clearTimeout(this.scrollTimer);
			this.scrollTimer = null;
		}
	},

	watch: {}
}
</script>

<style scoped lang="scss">
.page {
	min-height: 100vh;
	position: relative;
	padding-bottom: 200rpx;
}

.header {
	width: 750rpx;
	height: 376rpx;
	position: absolute;
	top: -300rpx;
	left: 0;
	z-index: -999;
}

.header image {
	width: 100%;
	height: 100%;
}

.content {
	margin-top: 280rpx;
}

.card {
	margin-left: 32rpx;
	width: 686rpx;
	background: #FFFFFF;
	box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
	border-radius: 16rpx;
	padding: 40rpx;
}

.card .top {
	padding-bottom: 40rpx;
	border-bottom: 2rpx solid #F2F3F6;
}

.card .top .title {
	font-size: 36rpx;
	font-weight: 500;
	color: #171717;
	letter-spacing: 2rpx;
}

.card .top .price {
	margin-top: 12rpx;
	font-size: 30rpx;
	font-weight: 500;
	color: #E72427;
}

.card .bottom {
	padding-top: 24rpx;
	display: flex;
}

.card .bottom .left {
	font-size: 24rpx;
	font-weight: 400;
	color: #999999;
	padding-top: 10rpx;
}

.card .bottom .right {
	flex: 1;
	margin-left: 20rpx;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.card .bottom .right .tag {
	width: fit-content;
	height: 44rpx;
	padding: 0 12rpx;
	background: #DCEAFF;
	border-radius: 4rpx;
	font-size: 16rpx;
	font-weight: 400;
	color: #2E80FE;
	line-height: 44rpx;
	text-align: center;
	margin: 10rpx;
}

.chol .choose {
	padding: 40rpx 32rpx;
}

.chol .choose .title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

.chol .choose .title span {
	color: #E72427;
}

.chol .choose .input-container {
	margin-top: 40rpx;
	position: relative;
	width: 100%;
	min-height: 88rpx;
}

.chol .choose .form-input {
	box-sizing: border-box;
	width: 100%;
	height: 88rpx;
	background: #F7F7F7;
	border-radius: 12rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	line-height: 88rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
	position: relative;
	z-index: 1;
}

.chol .choose .form-input:focus {
	background: #fff;
	border-color: #2E80FE;
	box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
	outline: none;
}

.chol .choose .desc {
	margin-top: 20rpx;
	font-size: 24rpx;
	font-weight: 400;
	color: #ADADAD;
}

.chol .choose .up {
	margin-bottom: 40rpx;
}

.chol .choose .cho_box {
	margin-top: 20rpx;
	display: flex;
	flex-wrap: wrap;
}

.chol .choose .cho_box .box_item {
	width: fit-content;
	padding: 0 20rpx;
	height: 60rpx;
	background: #FFFFFF;
	border-radius: 4rpx;
	border: 2rpx solid #D8D8D8;
	font-size: 24rpx;
	font-weight: 400;
	color: #ADADAD;
	line-height: 60rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	position: relative;
}

.chol .choose .cho_box .box_item .ok {
	width: 20rpx;
	height: 20rpx;
	position: absolute;
	right: 0;
	bottom: 0;
	background-color: #2E80FE;
	display: flex;
	align-items: center;
	justify-content: center;
}

.chol .fg {
	width: 750rpx;
	height: 20rpx;
	background: #F3F4F5;
}

.footer {
	padding: 20rpx 32rpx;
	width: 750rpx;
	background: #ffffff;
	box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
	position: fixed;
	bottom: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	z-index: 999;
	transition: bottom 0.25s ease;

	.footer-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 88rpx;
		transition: all 0.2s ease;
	}

	// 客服按钮 - 小占比
	.footer-service {
		flex: 0 0 100rpx;

		.footer-icon {
			margin-bottom: 4rpx;
		}

		.footer-text {
			font-size: 20rpx;
			color: #666;
		}
	}

	// 购物车按钮 - 小占比
	.footer-cart {
		flex: 0 0 100rpx;

		.footer-icon {
			margin-bottom: 4rpx;
		}

		.footer-text {
			font-size: 20rpx;
			color: #666;
		}
	}

	// 加入购物车按钮 - 大占比，无填充
	.footer-add-cart {
		flex: 1;
		margin: 0 16rpx;
		background: transparent;
		border: 2rpx solid #2e80fe;
		border-radius: 44rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #2e80fe;
		text-align: center;
		line-height: 84rpx;

		&:active {
			background: rgba(46, 128, 254, 0.1);
		}
	}

	// 立即下单按钮 - 大占比，有填充
	.footer-order {
		flex: 1;
		background: #2e80fe; // 修改为蓝色背景
		border-radius: 44rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #ffffff;
		text-align: center;
		line-height: 88rpx;

		&.submitting {
			background: #a5c7ff; // 修改为淡蓝色
			opacity: 0.7;
			pointer-events: none;
		}

		&:active {
			background: #1a6bd8; // 修改为深蓝色
		}
	}
}

// 当type=0时，只显示立即下单按钮，让其占满整个宽度
.footer-single-button .footer-order {
	margin: 0;
	flex: 1;
}

/* iOS安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
	.footer {
		padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
	}
}

/* 购物车弹窗样式 */
.cart-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.cart-modal-content {
	width: 100%;
	max-height: 90vh;
	/* 增加高度 */
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	/*减小圆角 */
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

.modal-header {
	padding: 24rpx 32rpx;
	/* 调整内边距 */
	display: flex;
	align-items: flex-start;
	/* 调整对齐方式 */
	justify-content: space-between;
	border-bottom: 1rpx solid #f5f5f5;
	/* 更细的边框 */
	position: relative;
	/* 添加相对定位 */
}

.modal-service-info {
	display: flex;
	align-items: flex-start;
	/* 调整对齐方式 */
	width: 100%;
	padding: 16rpx 0;
	flex-wrap: wrap;
	/* 允许换行 */
}

.modal-service-image {
	width: 120rpx;
	/* 增大图片尺寸 */
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 24rpx;
	flex-shrink: 0;
	/* 防止图片缩小 */
}

.modal-service-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.modal-service-title {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 12rpx;
}

.modal-service-price {
	font-size: 36rpx;
	/* 增大价格字体 */
	font-weight: 600;
	color: #FF4D4F;
	/* 更鲜艳的红色 */
	margin-bottom: 24rpx;
	/* 增加间距 */
}

.modal-close {
	position: absolute;
	top: 24rpx;
	right: 24rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.modal-scroll-content {
	flex: 1;
	overflow-y: auto;
	padding: 0 32rpx;
}

.modal-selected-info {
	padding: 32rpx 0;
	border-bottom: 2rpx solid #f5f5f5;
	display: flex;
	align-items: flex-start;
}

.modal-selected-title {
	font-size: 28rpx;
	color: #999;
	margin-right: 20rpx;
	margin-top: 10rpx;
}

.modal-selected-tags {
	flex: 1;
	display: flex;
	flex-wrap: wrap;
}

.modal-tag {
	padding: 8rpx 16rpx;
	background: #DCEAFF;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #2E80FE;
	margin: 8rpx 16rpx 8rpx 0;
}

.modal-chol {
	border-bottom: 2rpx solid #f5f5f5;
}

.modal-choose {
	padding: 32rpx 0;
}

.modal-choose-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
}

.modal-choose-title span {
	color: #E72427;
}

.modal-choose-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 24rpx;
}

.modal-cho-box {
	display: flex;
	flex-wrap: wrap;
}

.modal-box-item {
	padding: 16rpx 24rpx;
	background: #f8f8f8;
	border: 2rpx solid #e5e5e5;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #666;
	margin: 0 16rpx 16rpx 0;
	position: relative;
	transition: all 0.2s ease;
}

.modal-ok {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 24rpx;
	height: 24rpx;
	background: #2E80FE;
	border-radius: 0 8rpx 0 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-input-container {
	margin-top: 16rpx;
}

.modal-form-input {
	width: 100%;
	height: 88rpx;
	background: #f8f8f8;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	padding: 0 24rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.modal-form-input:focus {
	border-color: #2E80FE;
	background: #fff;
}

.modal-footer {
	padding: 32rpx;
	background: #fff;
	border-top: 2rpx solid #f5f5f5;
	flex-shrink: 0;
}

.modal-add-cart-btn {
	width: 100%;
	height: 88rpx;
	background: #E72427;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;

	&.submitting {
		background: #f5a5a7;
		opacity: 0.7;
		pointer-events: none;
	}
}

.modal-add-cart-btn:active {
	background: #c41e20;
}

/* 新增：下单弹窗样式 */
.modal-selected-section {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.modal-quantity-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 0;
	/* margin-top: 16rpx; remove this margin-top */
	width: 100%;
	/* Ensure it takes full width within service-details */
}


.modal-quantity-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.modal-quantity-control {
	display: flex;
	align-items: center;
}

.quantity-btn {
	width: 56rpx;
	height: 56rpx;
	border: 1rpx solid #e5e5e5;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #333;
	background: #fff;
}

.quantity-btn:active {
	background: #f5f5f5;
}

.quantity-input {
	width: 80rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333;
	margin: 0 20rpx;
}

.modal-address-section,
.modal-time-section {
	padding: 24rpx;
	padding-right: 80rpx;
	border-bottom: 1rpx solid #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.modal-section-title {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.section-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.modal-address-content,
.modal-time-content {
	flex: 1;
	margin: 0 16rpx;
	text-align: right;
}

.address-text {
	font-size: 28rpx;
	color: #2E80FE;
	margin-bottom: 4rpx;
}

.address-detail {
	font-size: 24rpx;
	color: #999;
}

.modal-time-content text {
	font-size: 28rpx;
	color: #2E80FE;
}

.modal-urgent-section {
	padding: 16rpx 0;
	/* Adjusted padding */
	border-bottom: none;
	/* Removed border, it's now inside modal-service-info */
	width: 100%;
	/* Ensure it takes full width within service-details */
}

.modal-urgent-checkbox {
	display: flex;
	align-items: center;
}

.checkbox-icon {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #e5e5e5;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
	background: #fff;
}

.checkbox-icon.checked {
	background: #2E80FE;
	border-color: #2E80FE;
}

.checkbox-label {
	font-size: 28rpx;
	color: #333;
}

.modal-notes-section {
	padding: 32rpx 0;
	margin-right: 60rpx;

}

.modal-notes-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 24rpx;
}

.modal-notes-textarea {
	width: 100%;
	min-height: 160rpx;
	background: #f8f8f8;
	border: 1rpx solid #e5e5e5;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;

	box-sizing: border-box;
}

.modal-notes-textarea:focus {
	border-color: #2E80FE;
	background: #fff;
}

.modal-total-price {
	font-size: 32rpx;
	font-weight: 600;
	color: #E72427;
	margin-bottom: 24rpx;
}

.modal-footer-buttons {
	display: flex;
	gap: 24rpx;
}

.modal-footer-buttons .modal-add-cart-btn {
	flex: 1;
	background: transparent;
	border: 2rpx solid #2E80FE;
	color: #2E80FE;
}

.modal-footer-buttons .modal-add-cart-btn:active {
	background: rgba(46, 128, 254, 0.1);
}

.modal-order-btn {
	width: 100%;
	height: 88rpx;
	background: #FF4D4F;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;

	&.submitting {
		background: #f5a5a7;
		opacity: 0.7;
		pointer-events: none;
	}
}

.modal-order-btn:active {
	background: #c41e20;
}

/* 时间选择弹窗样式 */
.time-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.time-modal-content {
	width: 100%;
	max-height: 80vh;
	background: #ffffff;
	border-radius: 32rpx 32rpx 0 0;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease-out;
}

.time-modal-header {
	padding: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 2rpx solid #f5f5f5;
	flex-shrink: 0;
}

.time-modal-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.time-modal-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.time-date-section {
	padding: 32rpx;
	display: flex;
	justify-content: space-around;
	align-items: center;
	border-bottom: 2rpx solid #f5f5f5;
}

.time-date-item {
	text-align: center;
	font-size: 28rpx;
	color: #333;
	padding: 16rpx 24rpx;
	border-radius: 12rpx;
	transition: all 0.2s ease;
}

.time-date-item.active {
	color: #2E80FE;
	background: rgba(46, 128, 254, 0.1);
}

.time-date-item.active .date-str {
	color: #2E80FE;
}

.date-str {
	font-weight: 500;
	margin-bottom: 8rpx;
}

.date-num {
	font-size: 24rpx;
	color: #666;
}

.time-date-item.active .date-num {
	color: #2E80FE;
}

.time-slots-section {
	flex: 1;
	padding: 32rpx;
	max-height: 400rpx;
}

.time-slots-grid {
	display: flex;
	justify-content: space-between;
	gap: 24rpx;
}

.time-slot-column {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.time-slot-item {
	height: 80rpx;
	background: #f8f8f8;
	border: 2rpx solid #e5e5e5;
	border-radius: 12rpx;
	font-size: 24rpx;
	color: #333;
	text-align: center;
	line-height: 76rpx;
	transition: all 0.2s ease;
}

.time-slot-item.active {
	background: #2E80FE;
	border-color: #2E80FE;
	color: #fff;
}

.time-slot-item.disabled {
	background: #f0f0f0;
	border-color: #e0e0e0;
	color: #ccc;
	pointer-events: none;
}

.time-modal-footer {
	padding: 32rpx;
	border-top: 2rpx solid #f5f5f5;
	flex-shrink: 0;
}

.time-confirm-btn {
	width: 100%;
	height: 88rpx;
	background: #2E80FE;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.time-confirm-btn:active {
	background: #1a6bd8;
}

/* 添加新增样式 */
.modal-header-tip {
	font-size: 28rpx;
	color: #333;
	text-align: center;
	width: 100%;
	padding: 16rpx 48rpx;
}

.modal-selected-title {
	font-size: 28rpx;
	color: #666;
}

.modal-specs-section {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f5f5f5;
}

.modal-specs-tags {
	display: flex;
	flex-wrap: wrap;
	margin-top: 16rpx;
}

.modal-spec-tag {
	padding: 8rpx 20rpx;
	background: #f0f9ff;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #2E80FE;
	margin: 8rpx 16rpx 8rpx 0;
}

.modal-notes-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 16rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.notes-limit {
	font-size: 24rpx;
	color: #999;
	font-weight: normal;
}

.textarea-counter {
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
}

.upload-photo-btn {
	width: 160rpx;
	height: 160rpx;
	background: #f8f8f8;
	border: 1rpx dashed #ddd;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-top: 24rpx;
}

.upload-photo-btn text {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
}

.modal-notes-section {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f5f5f5;
}
</style>
```