{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?0e0f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?ddac", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?6e11", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?e313", "uni-app:///user/order_list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?de7d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/order_list.vue?19fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "status", "showConfirm", "showCancel", "showPaymentModal", "showRefundModal", "currentItem", "<PERSON><PERSON><PERSON><PERSON>", "reminddata", "paymentRemind", "huodong<PERSON>", "isFromTiaozhuan", "tmplIds", "list", "name", "value", "currentIndex", "page", "orderList", "pay_typeArr", "id", "isLoading", "onPullDownRefresh", "uni", "onReachBottom", "setTimeout", "payType", "pageNum", "pageSize", "item", "console", "methods", "huo<PERSON>click", "icon", "title", "gohuodongevaluate", "url", "<PERSON><PERSON><PERSON>", "confirmPayment", "huodongwanchengclick", "content", "confirmText", "cancelText", "success", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dingyue", "templateId", "templateCategoryId", "selectedTmplIds", "fail", "updateHighlight", "userId", "role", "goevaluate", "applyT", "confirmRefund", "goChoose", "confirmorder", "confirmconfirm", "orderId", "duration", "confirmCancel", "cancelorder", "goUrl", "getList", "huodongRes", "userOrderRes", "Array", "handleHeader", "getcommissionRatio", "onLoad", "onShow", "onBackPress", "onUnload", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsK72B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,UACA,gDACA,+CACA,+CACA,8CACA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;MACAC;IACA;MACAA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;QACA;QACA;UAAA,uCACAC;YACAH;UAAA;QAAA,CACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACAI;MACA;IACA;EACA;EACAC;IACAC;MACAT;QACAU;QACAC;MACA;IACA;IACAC;MACAZ;QACAa;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;QACAf;UACAa;QACA;MACA;IACA;IACAG;MAAA;MACAhB;QACAW;QACAM;QACAC;QACAC;QACAC;UACA;YACA;cACAvB;YACA;cACA;gBACAG;kBACAU;kBACAC;gBACA;gBACA;gBACA;gBACA;cACA;gBACAX;kBACAU;kBACAC;gBACA;cACA;YACA;cACAX;gBACAU;gBACAC;cACA;cACAJ;YACA;UACA;QACA;MACA;IACA;IACAc;MAAA;MACA;MACA;QACAxB;MACA;QACAG;UACAU;UACAC;QACA;QACA;QACA;QACA;MACA;QACAX;UACAU;UACAC;QACA;QACAJ;MACA;IACA;IACAe;MAAA;MACA;MACA;MACA;QACAf;QACA;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;QAAA;UACAgB;UACAC;QACA;MAAA;MAEAxB;QACAX;QACA+B;UACA;UACAK;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;YACA;UACA;QACA;QACAC;MACA;IAEA;IACAC;MACA;MACA;QACApB;QACA;MACA;MACA;QACAqB;QACAC;QACA1B;MACA;QACAI;MACA;QACAA;MACA;IACA;IACAuB;MACA9B;QACAa;MACA;IACA;IACAkB;MACA;QACA/B;UACAU;UACAC;QACA;MACA;QACAX;UACAU;UACAC;QACA;MACA;QACAX;UACAU;UACAC;QACA;MACA;QACA;QACA;MACA;IACA;IACAqB;MACA;MACA;QACAhC;UACAa;QACA;MACA;IACA;IACAoB;MACA;MACAjC;QACAa;MACA;IACA;IACAqB;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACA;UACApC;YACAU;YACAC;YACA0B;UACA;QACA;UACArC;YACAU;YACAC;UACA;UACAX;YACAa;UACA;QACA;MACA;QACAb;UACAU;UACAC;QACA;QACAJ;MACA;IACA;IACA+B;MAAA;MACA;MACA;QACAzC;MACA;QACAG;UACAU;UACAC;QACA;QACA;QACA;QACA;MACA;QACAX;UACAU;UACAC;QACA;QACAJ;MACA;IACA;IACAgC;MACA;MACA;IACA;IACAC;MACAxC;QACAa;MACA;IACA;IACA4B;MAAA;MACA;MACA;QACA,oBACA,kCACA;UACAtC;UACAC;UACAC;QACA,GACA;UAAA;YAAAqC;YAAAC;UACA,iEACAC;YAAA;UAAA,MACAF,gBACA;UACA;UACA;UACA;YAAA,uCACApC;cACAH;YAAA;UAAA,CACA;UACA;UACA;UACA;QACA;UACAH;YACAU;YACAC;UACA;UACAJ;UACA;UACA;UACA;QACA;MACA;QACA;QACA;UACAJ;UACAC;UACAC;QACA;UACA;UACA;YAAA,uCACAC;cACAH;YAAA;UAAA,CACA;UACA;UACA;QACA;UACAH;YACAU;YACAC;UACA;UACAJ;UACA;UACA;QACA;MACA;IACA;IACAsC;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACAvC;MACA;IACA;EACA;EACAwC;IAAA;IACA;MACA;MACAxC;IACA;MACA;MACAA;IACA;IACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAyC;IAAA;IACAhD;MACA;MACA;MACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;MACA;IACA;EACA;EACAiD;IACAjD;MACAa;IACA;IACA;EACA;EACAqC;IACA;MACAlD;QACAa;MACA;IACA;EACA;EACAsC;IACA1D;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtmBA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/order_list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/order_list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order_list.vue?vue&type=template&id=1ee6bdd6&scoped=true&\"\nvar renderjs\nimport script from \"./order_list.vue?vue&type=script&lang=js&\"\nexport * from \"./order_list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ee6bdd6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/order_list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=template&id=1ee6bdd6&scoped=true&\"", "var components\ntry {\n  components = {\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var l1 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 =\n      item.payType >= -1 && !(item.payType === -1)\n        ? parseInt(item.payType)\n        : null\n    var m1 =\n      item.payType >= -1\n        ? item.payType === 0 ||\n          (item.payType === 1 && parseInt(item.payType) >= 1)\n        : null\n    var m2 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) !== 5\n        : null\n    var m3 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) === 5\n        : null\n    var m4 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 1 && parseInt(item.type) === 5\n        : null\n    var m5 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.type) !== 5\n        : null\n    var m6 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.payType) !== 6 &&\n          parseInt(item.type) !== 5\n        : null\n    var m7 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.payType) !== 6 &&\n          parseInt(item.type) === 5\n        : null\n    var m8 =\n      item.payType >= -1\n        ? parseInt(item.payType) >= 2 &&\n          parseInt(item.payType) !== 7 &&\n          parseInt(item.payType) !== 6 &&\n          parseInt(item.type) === 5\n        : null\n    var m9 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 0 &&\n          item.type === 5\n        : null\n    var m10 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 1 &&\n          item.type === 5\n        : null\n    var m11 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 0 &&\n          item.type !== 5\n        : null\n    var m12 =\n      item.payType >= -1\n        ? parseInt(item.payType) === 7 &&\n          item.isComment === 1 &&\n          item.type !== 5\n        : null\n    var g1 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var g2 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var l0 = !(item.payType >= -1)\n      ? _vm.__map(item.quotedPriceVos, function (shfItem, shfIndex) {\n          var $orig = _vm.__get_orig(shfItem)\n          var g3 = (shfItem.price * (1 + _vm.jiaNum / 100)).toFixed(2)\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n    var g4 = !(item.payType >= -1) ? item.quotedPriceVos.length : null\n    var g5 = !(item.payType >= -1) && g4 > 0 ? item.quotedPriceVos.length : null\n    var m13 = !(item.payType >= -1)\n      ? item.payType === -3 && parseInt(item.type) !== 5\n      : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n      m6: m6,\n      m7: m7,\n      m8: m8,\n      m9: m9,\n      m10: m10,\n      m11: m11,\n      m12: m12,\n      g1: g1,\n      g2: g2,\n      l0: l0,\n      g4: g4,\n      g5: g5,\n      m13: m13,\n    }\n  })\n  var g6 = _vm.orderList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.type !== 5 ? _vm.goUrl(\"/user/order_details?id=\" + item.id) : null\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCancel = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showConfirm = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showPaymentModal = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showRefundModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g6: g6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"handleHeader(item)\">\n\t\t\t\t<view :style=\"currentIndex === item.value ? 'color:#2E80FE;' : ''\">{{ item.name }}</view>\n\t\t\t\t<view class=\"blue\" :style=\"currentIndex === item.value ? '' : 'background-color:#fff;'\"></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-empty mode=\"order\" icon=\"http://cdn.uviewui.com/uview/empty/order.png\" v-if=\"orderList.length === 0\">\n\t\t</u-empty>\n\n\t\t<view @click=\"dingyue()\" class=\"main\">\n\t\t\t<view v-for=\"(item, index) in orderList\" :key=\"index\">\n\t\t\t\t<view class=\"main_item\" v-if=\"item.payType >= -1 \"\n\t\t\t\t\t@click=\"item.type !== 5 ? goUrl(`/user/order_details?id=${item.id}`) : null\">\n\t\t\t\t\t<view class=\"head\">\n\t\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t\t<view class=\"type\">{{ item.payType === -1 ? '已取消' : pay_typeArr[parseInt(item.payType)] }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t\t\t<text style=\"color:#F60100 ;\" v-if=\"item.type===5\">活动订单</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"righ\"\n\t\t\t\t\t\t\tv-if=\"item.payType === 0 || (item.payType === 1 && parseInt(item.payType) >= 1)\">\n\t\t\t\t\t\t\t<view>￥{{ item.payPrice }}</view>\n\t\t\t\t\t\t\t<view>x{{ item.num ? item.num : 1 }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t\t<text>{{ item.createTime }}</text>\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1&&parseInt(item.type) !== 5\"\n\t\t\t\t\t\t\************=\"gozhifu(item)\">\n\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t<!-- \t\t<view class=\"qzf\" v-if=\"\"\n\t\t\t\t\t\t\************=\"gozhifu(item)\">\n\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t -->\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1&&parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"goUrl(`/user/huodongCashier?id=${item.id}&price=${item.payPrice}&type=${item.payType}&goodsId=${item.goodsId}`)\">\n\t\t\t\t\t\t\t去支付\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qzf\" v-if=\"parseInt(item.payType) === 1&&parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongquxiaos(item)\">\n\t\t\t\t\t\t\t取消订单\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.type) !== 5\"\n\t\t\t\t\t\t\************=\"confirmorder(item)\">\n\t\t\t\t\t\t\t确认完成\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) !== 5\"\n\t\t\t\t\t\t\************=\"applyT(item)\">\n\t\t\t\t\t\t\t申请退款\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongwanchengclick(item)\">\n\t\t\t\t\t\t\t确认完成\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"color: #999999; background-color: #f0f0f0;\" class=\"qrwc\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) >= 2 && parseInt(item.payType) !== 7&& parseInt(item.payType) !== 6&& parseInt(item.type) === 5\"\n\t\t\t\t\t\t\************=\"huodongclick()\">\n\t\t\t\t\t\t\t待上门\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\" v-if=\"parseInt(item.payType) === 7 && item.isComment === 0&& item.type === 5\"\n\t\t\t\t\t\t\************=\"gohuodongevaluate(item)\">去评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) === 7 && item.isComment === 1 && item.type === 5\">\n\t\t\t\t\t\t\t已评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) === 7 && item.isComment === 0 && item.type !== 5\"\n\t\t\t\t\t\t\************=\"goevaluate(item)\">去评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qpl\"\n\t\t\t\t\t\t\tv-if=\"parseInt(item.payType) === 7 && item.isComment === 1 && item.type !== 5\">\n\t\t\t\t\t\t\t已评价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"main_item_already\" v-else @click=\"goChoose(item)\">\n\t\t\t\t\t<view style=\"font-size: 32rpx;font-weight: 500;\" class=\"title\">\n\t\t\t\t\t\t{{ item.quotedPriceVos.length === 0 ? '等待师傅报价' : '等待您选择师傅' }}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"ok\" v-if=\"item.quotedPriceVos.length > 0\">已有师傅报价</view>\n\t\t\t\t\t<view class=\"no\">单号：{{ item.orderCode }}</view>\n\t\t\t\t\t<view class=\"mid\">\n\t\t\t\t\t\t<view class=\"lef\">\n\t\t\t\t\t\t\t<image :src=\"item.goodsCover\" mode=\"\"></image>\n\t\t\t\t\t\t\t<text>{{ item.goodsName }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"bot\">\n\t\t\t\t\t\t<text>{{ item.createTime }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"shifu\">\n\t\t\t\t\t\t<scroll-view scroll-x=\"true\">\n\t\t\t\t\t\t\t<view class=\"shifu_item\" v-for=\"(shfItem, shfIndex) in item.quotedPriceVos\" :key=\"shfIndex\">\n\t\t\t\t\t\t\t\t<image :src=\"shfItem.selfImg ? shfItem.selfImg : '/static/mine/default_user.png'\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t<text>￥{{ (shfItem.price * (1 + jiaNum / 100)).toFixed(2) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</scroll-view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.quotedPriceVos.length > 0\"\n\t\t\t\t\t\tstyle=\"display: flex; justify-content: center; align-items: center; margin-top: 20rpx;\">\n\t\t\t\t\t\t<view class=\"qxdd\" @click.stop=\"cancelorder(item)\">取消订单</view>\n\t\t\t\t\t\t<view style=\"margin-left: 20%;\" class=\"tips\" vif=\"item.quotedPriceVos.length > 0\">\n\t\t\t\t\t\t\t{{ item.quotedPriceVos.length }}位师傅已报价\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"qxdd\" style=\"margin-left: 20rpx;\">\n\t\t\t\t\t\t\t选择师傅\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.payType===-3&& parseInt(item.type) !== 5\" class=\"qxdd\"\n\t\t\t\t\t\************=\"cancelorder(item)\">取消订单</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-modal :show=\"showCancel\" title=\"取消订单\" content=\"确认要取消该订单吗\" showCancelButton @cancel=\"showCancel = false\"\n\t\t\t@confirm=\"confirmCancel\"></u-modal>\n\t\t<u-modal :show=\"showConfirm\" title=\"完成订单\" content=\"确认要完成该订单吗\" showCancelButton @cancel=\"showConfirm = false\"\n\t\t\t@confirm=\"confirmconfirm\"></u-modal>\n\n\t\t<u-modal :show=\"showPaymentModal\" title=\"提示\"  showCancelButton   confirm-text=\"去支付\"\n\t\t\t@cancel=\"showPaymentModal = false\" @confirm=\"confirmPayment\">\n\t\t\t\n\t\t\t<view class=\"modal-content-red\">\n\t\t\t\t{{ paymentRemind }}\n\t\t\t</view>\n\t\t\t\n\t\t\t</u-modal>\n\n\t\t<u-modal :show=\"showRefundModal\" title=\"提示\" showCancelButton\n\t\t\t@cancel=\"showRefundModal = false\" @confirm=\"confirmRefund\">\n\t\t\t<view class=\"modal-content-red\">\n\t\t\t\t{{ reminddata }}\n\t\t\t</view>\n\t\t</u-modal>\n\t\t<view style=\"display: flex; justify-content: center;\" v-if=\"orderList.length >= 10\">\n\t\t\t<u-loadmore :status=\"status\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatus: 'loadmore',\n\t\t\t\tshowConfirm: false,\n\t\t\t\tshowCancel: false,\n\t\t\t\tshowPaymentModal: false,\n\t\t\t\tshowRefundModal: false,\n\t\t\t\tcurrentItem: null,\n\t\t\t\tjiaNum: 0,\n\t\t\t\treminddata: '若你选择线下交易，无平台监管遭遇诈骗或者纠纷需由您自行承担损失！',\n\t\t\t\tpaymentRemind: '无平台担保的支付可能遭遇“假维修”“小病大修”等套路（据消协数，40%的线下维修投诉涉及虚报故障）',\n\t\t\t\thuodonglist: [],\n\t\t\t\tisFromTiaozhuan: false,\n\t\t\t\ttmplIds: [\n\t\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tlist: [{\n\t\t\t\t\t\tname: '全部',\n\t\t\t\t\t\tvalue: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '报价列表',\n\t\t\t\t\t\tvalue: -2\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '待支付',\n\t\t\t\t\t\tvalue: 1\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '待服务',\n\t\t\t\t\t\tvalue: 5\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '服务中',\n\t\t\t\t\t\tvalue: 6\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '已完成',\n\t\t\t\t\t\tvalue: 7\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\tcurrentIndex: 0,\n\t\t\t\tpage: 1,\n\t\t\t\torderList: [],\n\t\t\t\tpay_typeArr: ['', '待支付', '报价列表', '已接单', '上门中', '待服务', '服务中', '已完成'],\n\t\t\t\tid: '',\n\t\t\t\tisLoading: false\n\t\t\t};\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.page = 1;\n\t\t\tthis.orderList = [];\n\t\t\tthis.status = 'loadmore';\n\t\t\tthis.getList(this.currentIndex).then(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}).catch(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\tonReachBottom() {\n\t\t\tif (this.status === 'nomore' || this.isLoading) return;\n\t\t\tthis.isLoading = true;\n\t\t\tthis.status = 'loading';\n\t\t\tthis.page++;\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.$api.service.userOrder({\n\t\t\t\t\tpayType: this.currentIndex,\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: 10\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconst list = Array.isArray(res.list) ? res.list : [];\n\t\t\t\t\tconst normalizedList = list.map(item => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t}));\n\t\t\t\t\tthis.orderList = [...this.orderList, ...normalizedList];\n\t\t\t\t\tthis.status = list.length < 10 ? 'nomore' : 'loadmore';\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tthis.page--;\n\t\t\t\t\tconsole.error('Error loading more:', err);\n\t\t\t\t});\n\t\t\t}, 1500);\n\t\t},\n\t\tmethods: {\n\t\t\thuodongclick() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\ttitle: '耐心等待师傅上门，有问题联系客服'\n\t\t\t\t});\n\t\t\t},\n\t\t\tgohuodongevaluate(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}&huodong=${1}`\n\t\t\t\t});\n\t\t\t},\n\t\t\tgozhifu(item) {\n\t\t\t\tthis.currentItem = item;\n\t\t\t\tthis.showPaymentModal = true;\n\t\t\t},\n\t\t\tconfirmPayment() {\n\t\t\t\tthis.showPaymentModal = false;\n\t\t\t\tif (this.currentItem) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/user/Cashier?id=${this.currentItem.id}&price=${this.currentItem.payPrice}&type=${this.currentItem.payType}&goodsId=${this.currentItem.goodsId}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thuodongwanchengclick(item) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认完成',\n\t\t\t\t\tcontent: '师傅是否已完成订单？',\n\t\t\t\t\tconfirmText: '确定',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.$api.service.huodongqueding({\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\t\t\ttitle: '订单完成'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tthis.page = 1;\n\t\t\t\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: err.msg || '操作失败'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\thuodongquxiaos(item) {\n\t\t\t\tthis.showCancel = false;\n\t\t\t\tthis.$api.service.huodongquxiao({\n\t\t\t\t\tid: item.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t});\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t\t});\n\t\t\t},\n\t\t\tdingyue() {\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tconst requiredTmplId = '9sT8DPghuzkjRmg3gBefKWgrZHMIkJs0l7hZKgL5SWY';\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst otherTmplIds = allTmplIds.filter(id => id !== requiredTmplId);\n\t\t\t\tconst shuffled = otherTmplIds.sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = [requiredTmplId, ...shuffled.slice(0, 2)];\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tupdateHighlight(options) {\n\t\t\t\tconst userId = uni.getStorageSync('userId');\n\t\t\t\tif (!userId) {\n\t\t\t\t\tconsole.log('No userId, skipping updateHighlight');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.$api.service.updataHighlight({\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\trole: 1,\n\t\t\t\t\tpayType: options.tab\n\t\t\t\t}).then(res => {\n\t\t\t\t\tconsole.log('updateHighlight response:', res);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('updateHighlight error:', err);\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoevaluate(item) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/user/addevaluate?id=${item.id}&goodsId=${item.goodsId}`\n\t\t\t\t});\n\t\t\t},\n\t\t\tapplyT(item) {\n\t\t\t\tif (item.refundStatus === 2) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '您的退款正在审核中'\n\t\t\t\t\t});\n\t\t\t\t} else if (item.refundStatus === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '您的退款已审核通过，请留意账户动态'\n\t\t\t\t\t});\n\t\t\t\t} else if (item.refundStatus === 3) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '您的退款申请已被驳回，请重新申请'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentItem = item;\n\t\t\t\t\tthis.showRefundModal = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirmRefund() {\n\t\t\t\tthis.showRefundModal = false;\n\t\t\t\tif (this.currentItem) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/user/tuicause?order_id=${this.currentItem.id}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoChoose(item) {\n\t\t\t\tthis.$store.commit('changeOrderInfo', item);\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/user/choose_master'\n\t\t\t\t});\n\t\t\t},\n\t\t\tconfirmorder(item) {\n\t\t\t\tthis.id = item.id;\n\t\t\t\tthis.showConfirm = true;\n\t\t\t},\n\t\t\tconfirmconfirm() {\n\t\t\t\tthis.showConfirm = false;\n\t\t\t\tthis.$api.service.confirmOrder({\n\t\t\t\t\torderId: this.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (res === -1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '不在服务中不能确认完成',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '操作成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\turl: `/user/order_list?tab=7`\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '操作失败'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Confirm Error:', err);\n\t\t\t\t});\n\t\t\t},\n\t\t\tconfirmCancel() {\n\t\t\t\tthis.showCancel = false;\n\t\t\t\tthis.$api.service.cancelOrder({\n\t\t\t\t\tid: this.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '取消成功'\n\t\t\t\t\t});\n\t\t\t\t\tthis.page = 1;\n\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: '取消失败'\n\t\t\t\t\t});\n\t\t\t\t\tconsole.error('Cancel Error:', err);\n\t\t\t\t});\n\t\t\t},\n\t\t\tcancelorder(item) {\n\t\t\t\tthis.id = item.id;\n\t\t\t\tthis.showCancel = true;\n\t\t\t},\n\t\t\tgoUrl(e) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: e\n\t\t\t\t});\n\t\t\t},\n\t\t\tgetList(nval) {\n\t\t\t\tconst payType = nval !== undefined ? nval : this.currentIndex;\n\t\t\t\tif (payType === 0) {\n\t\t\t\t\treturn Promise.all([\n\t\t\t\t\t\tthis.$api.service.huodongorder(),\n\t\t\t\t\t\tthis.$api.service.userOrder({\n\t\t\t\t\t\t\tpayType: 0,\n\t\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\t\tpageSize: 10\n\t\t\t\t\t\t})\n\t\t\t\t\t]).then(([huodongRes, userOrderRes]) => {\n\t\t\t\t\t\tconst huodongList = (huodongRes.code === \"200\" && huodongRes.data) ?\n\t\t\t\t\t\t\t(Array.isArray(huodongRes.data) ? huodongRes.data.filter(item => item != null) : [\n\t\t\t\t\t\t\t\thuodongRes.data\n\t\t\t\t\t\t\t]) : [];\n\t\t\t\t\t\tthis.huodonglist = huodongList;\n\t\t\t\t\t\tconst userList = Array.isArray(userOrderRes.list) ? userOrderRes.list : [];\n\t\t\t\t\t\tconst normalizedUserList = userList.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tconst combinedList = [...huodongList, ...normalizedUserList];\n\t\t\t\t\t\tthis.$set(this, 'orderList', combinedList);\n\t\t\t\t\t\tthis.status = userList.length < 10 ? 'nomore' : 'loadmore';\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\ttitle: '获取订单失败'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.error('API Error:', err);\n\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t\tthis.huodonglist = [];\n\t\t\t\t\t\treturn Promise.reject(err);\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.huodonglist = [];\n\t\t\t\t\treturn this.$api.service.userOrder({\n\t\t\t\t\t\tpayType,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: 10\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\tconst list = Array.isArray(res.list) ? res.list : [];\n\t\t\t\t\t\tconst normalizedList = list.map(item => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\tpayType: parseInt(item.payType)\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tthis.$set(this, 'orderList', normalizedList);\n\t\t\t\t\t\tthis.status = list.length < 10 ? 'nomore' : 'loadmore';\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\t\ttitle: '获取订单失败'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.error('API Error:', err);\n\t\t\t\t\t\tthis.orderList = [];\n\t\t\t\t\t\treturn Promise.reject(err);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleHeader(item) {\n\t\t\t\tthis.currentIndex = item.value;\n\t\t\t},\n\t\t\tgetcommissionRatio() {\n\t\t\t\tthis.$api.service.commissionRatio().then(res => {\n\t\t\t\t\tthis.jiaNum = res;\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tconsole.error('getcommissionRatio Error:', err);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tif (options.from && options.from === 'tiaozhuan') {\n\t\t\t\tthis.isFromTiaozhuan = true;\n\t\t\t\tconsole.log('来源是跳转页，返回时将执行默认返回');\n\t\t\t} else {\n\t\t\t\tthis.isFromTiaozhuan = false;\n\t\t\t\tconsole.log('来源是其他页面，返回时将跳转到\"我的\"页面');\n\t\t\t}\n\t\t\tthis.$api.service.remind().then(res => {\n\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\tthis.reminddata = res.data.cancelRemind;\n\t\t\t\t\tthis.paymentRemind = res.data.paymentRemind;\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.updateHighlight(options);\n\t\t\tthis.currentIndex = options.tab ? parseInt(options.tab) : 0;\n\t\t\tthis.getList(this.currentIndex);\n\t\t\tthis.getcommissionRatio();\n\t\t},\n\t\tonShow() {\n\t\t\tuni.$on('cancelOr', () => {\n\t\t\t\tthis.currentIndex = 0;\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.getList();\n\t\t\t});\n\t\t\tconst pages = getCurrentPages();\n\t\t\tconst currentPage = pages[pages.length - 1];\n\t\t\tconst options = currentPage.options || {};\n\t\t\tif (options.tab) {\n\t\t\t\tthis.currentIndex = parseInt(options.tab);\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.getList(this.currentIndex);\n\t\t\t}\n\t\t},\n\t\tonBackPress() {\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/mine'\n\t\t\t});\n\t\t\treturn true;\n\t\t},\n\t\tonUnload() {\n\t\t\tif (this.isFromTiaozhuan) {\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: '/pages/mine'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tcurrentIndex(nval) {\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.orderList = [];\n\t\t\t\tthis.status = 'loadmore';\n\t\t\t\tthis.getList(nval);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tbackground-color: #F8F8F8;\n\t\tmin-height: 100vh;\n\t\tpadding-top: 100rpx;\n\n\t\t.header {\n\t\t\tposition: fixed;\n\t\t\ttop: 0;\n\t\t\twidth: 750rpx;\n\t\t\theight: 100rpx;\n\t\t\tbackground: #FFFFFF;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-around;\n\t\t\talign-items: center;\n\t\t\tz-index: 99;\n\n\t\t\t.header_item {\n\t\t\t\tmax-width: 90rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t.blue {\n\t\t\t\t\tmargin-top: 8rpx;\n\t\t\t\t\twidth: 38rpx;\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\tborder-radius: 4rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.main {\n\t\t\tpadding: 40rpx 30rpx;\n\n\t\t\t.main_item {\n\t\t\t\twidth: 690rpx;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tpadding: 28rpx 36rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tbox-sizing: border-box;\n\n\t\t\t\t.head {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\n\t\t\t\t\t.no {\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mid {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.lef {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.righ {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\ttext-align: right;\n\t\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bot {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.qzf,\n\t\t\t\t\t.qxdd,\n\t\t\t\t\t.lxsf,\n\t\t\t\t\t.qrwc,\n\t\t\t\t\t.qpl {\n\t\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\t\theight: 48rpx;\n\t\t\t\t\t\tbackground: #2E80FE;\n\t\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t.qzf,\n\t\t\t\t\t.qrwc,\n\t\t\t\t\t.qpl {\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\n\t\t\t\t\t.qxdd,\n\t\t\t\t\t.lxsf {\n\t\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.main_item_already {\n\t\t\t\tpadding: 28rpx 36rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tbox-sizing: border-box;\n\n\t\t\t\t.qxdd {\n\t\t\t\t\twidth: 148rpx;\n\t\t\t\t\theight: 48rpx;\n\t\t\t\t\tbackground: #FFFFFF;\n\t\t\t\t\tborder-radius: 50rpx;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 48rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t}\n\n\t\t\t\t.no {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\n\t\t\t\t.mid {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.lef {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmargin-left: 30rpx;\n\t\t\t\t\t\t\tmax-width: 350rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bot {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\n\t\t\t\t.shifu {\n\t\t\t\t\tmargin-top: 20rpx;\n\n\t\t\t\t\t.shifu_item {\n\t\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tmargin-right: 20rpx;\n\n\t\t\t\t\t\timage {\n\t\t\t\t\t\t\twidth: 90rpx;\n\t\t\t\t\t\t\theight: 90rpx;\n\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.tips {\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t}\n\n\t\t\t\t.title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t}\n\n\t\t\t\t.ok {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* MODIFICATION START: Added style for the red modal content */\n\t.modal-content-red {\n\t\tcolor: #F60100; // A common red color for warnings\n\t\tpadding: 10rpx 30rpx; // Add some padding for better spacing\n\t\ttext-align: center; // Center the text within the modal\n\t}\n\t/* MODIFICATION END */\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order_list.vue?vue&type=style&index=0&id=1ee6bdd6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755411526361\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}