{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?c074", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?4ba4", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?d92b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?be1f", "uni-app:///user/edit_address.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?3176", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/edit_address.vue?696f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "showCity", "content", "title", "show", "id", "form", "userName", "mobile", "houseNumber", "address", "addressInfo", "city", "cityId", "lng", "lat", "userId", "sex", "status", "areaId", "provinceId", "cityIds", "createTime", "top", "uniacid", "columnsCity", "methods", "goMap", "uni", "scope", "success", "fail", "console", "icon", "duration", "getcity", "getCity", "then", "catch", "<PERSON><PERSON><PERSON><PERSON>", "index", "picker", "confirmCity", "map", "filter", "join", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "delta", "<PERSON><PERSON><PERSON><PERSON>", "phoneReg", "requiredFields", "key", "subForm", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6F/2B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;;MAEAC;QACAC;QACAC;UACAF;YACAE;cACA;cACA;cACA;cACA;YACA;YACAC;cACAC;cACAJ;gBACAK;gBACA9B;gBACA+B;cACA;YACA;UACA;QACA;QACAH;UACAC;UACAJ;YACAK;YACA9B;YACA+B;UACA;QACA;MACA;IAoBA;IACAC;MAAA;MACA;MACA,kBACAC,kBACAC;QAAA;QACA;UAAA;YACAhC;YACAF;UACA;QAAA;QACA;MACA,GACAkC;QAAA;QACA;UAAA;YACAhC;YACAF;UACA;QAAA;QACA;MACA,GACAkC;QACA;UAAA;YACAhC;YACAF;UACA;QAAA;QACA;MACA,GACAmC;QACAN;QACA;QACAJ;UACAK;UACA9B;UACA+B;QACA;MACA;IACA;IACAK;MAAA;MACA;QAAAC;QAAA;QAAAC;MACA;QACA;UAAA;UACA;YAAA;cACApC;cACAF;YACA;UAAA;UACAsC;UACA;UACA;YACA;cAAA;gBACApC;gBACAF;cACA;YAAA;YACAsC;YACA;UACA;QACA;MACA;QACA;UACA;YAAA;cACApC;cACAF;YACA;UAAA;UACAsC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA,6BACAC;QACA;UAAA;UACA;QACA;QACA;MACA,GACAC,gBACAC;;MAEA;MACA;QACA;UAAA;UACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAlB;kBACAK;kBACA9B;kBACA+B;gBACA;gBACAa;kBACAnB;oBAAAoB;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhB;gBACAJ;kBACAK;kBACA9B;kBACA+B;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACArB;kBACAK;kBACA9B;kBACA+B;gBACA;gBAAA;cAAA;gBAGAgB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAtB;kBACAK;kBACA9B;kBACA+B;gBACA;gBAAA;cAAA;gBAGAiB;gBAAA,0BACAA;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAAC;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAxB;kBACAK;kBACA9B;kBACA+B;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;cAAA;gBAKA;gBACAmB;kBACAhD;kBAAA;kBACAE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBAAA;kBACAO;kBACAD;kBACAE;kBAAA;kBACAP;kBACAC;kBACAC;kBACAC;kBACAC;kBACAI;kBAAA;kBACAC;kBACAC;gBACA;;gBAEAQ;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAJ;kBACAK;kBACA9B;kBACA+B;gBACA;gBACAa;kBACAnB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAI;gBACAJ;kBACAK;kBACA9B;kBACA+B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAoB;IACA;IACA;IACA;MACAtB;MACA;MACA,4CACA;QACAvB;QACAF;QACAC;QACAS;QACAP;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MAAA,EACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7YA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/edit_address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/edit_address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit_address.vue?vue&type=template&id=a5731d36&scoped=true&\"\nvar renderjs\nimport script from \"./edit_address.vue?vue&type=script&lang=js&\"\nexport * from \"./edit_address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit_address.vue?vue&type=style&index=0&id=a5731d36&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a5731d36\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/edit_address.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit_address.vue?vue&type=template&id=a5731d36&scoped=true&\"", "var components\ntry {\n  components = {\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-picker/u-picker\" */ \"uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-switch/u-switch\" */ \"uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.show = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showCity = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.form.sex = 1\n    }\n    _vm.e4 = function ($event) {\n      _vm.form.sex = 2\n    }\n    _vm.e5 = function ($event) {\n      _vm.show = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit_address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit_address.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <u-picker\n      :show=\"showCity && !loading\"\n      ref=\"uPicker\"\n      :loading=\"loading\"\n      :columns=\"columnsCity\"\n      @change=\"changeHandler\"\n      keyName=\"title\"\n      @cancel=\"showCity = false\"\n      @confirm=\"confirmCity\"\n    ></u-picker>\n    <u-modal\n      :show=\"show\"\n      :title=\"title\"\n      :content=\"content\"\n      @confirm=\"DelAddress\"\n      @cancel=\"show = false\"\n      showCancelButton\n    ></u-modal>\n    <view class=\"top\">个人信息隐私信息完全保密</view>\n    <view class=\"main\">\n      <view class=\"main_item\" @click=\"goMap\">\n        <view class=\"name\">服务地址</view>\n        <view class=\"address\">\n          <span>{{ form.address }}</span>\n        </view>\n        <image src=\"../static/images/position.png\" mode=\"\"></image>\n      </view>\n      <view class=\"main_item\">\n        <view class=\"name\">所在区域</view>\n        <input\n          type=\"text\"\n          v-model=\"form.city\"\n          placeholder=\"请选择所在区域\"\n          disabled\n          @click=\"showCity = true\"\n        />\n      </view>\n      <view class=\"main_item\">\n        <view class=\"name\">门牌号</view>\n        <input\n          type=\"text\"\n          v-model=\"form.houseNumber\"\n          placeholder=\"请输入详细地址，如7栋4单元18a\"\n        />\n      </view>\n      <view class=\"main_item\">\n        <view class=\"name\">联系人</view>\n        <input type=\"text\" v-model=\"form.userName\" placeholder=\"请输入姓名\" />\n      </view>\n      <view class=\"main_item\">\n        <view class=\"name\">性别</view>\n        <view class=\"box\">\n          <view\n            class=\"box_item\"\n            :style=\"\n              form.sex == 1\n                ? 'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE'\n                : ''\n            \"\n            @click=\"form.sex = 1\"\n          >\n            先生\n          </view>\n          <view\n            class=\"box_item\"\n            :style=\"\n              form.sex == 2\n                ? 'color:#2E80FE;background-color:#CCE0FF;border: 2rpx solid #2E80FE'\n                : ''\n            \"\n            @click=\"form.sex = 2\"\n          >\n            女士\n          </view>\n        </view>\n      </view>\n      <view class=\"main_item\">\n        <view class=\"name\">手机号码</view>\n        <input type=\"tel\" v-model=\"form.mobile\" placeholder=\"请输入手机号码\" />\n      </view>\n      <view class=\"main_item last\">\n        <view class=\"name\">设为默认地址</view>\n        <u-switch v-model=\"form.status\" activeColor=\"#2E80FE\"></u-switch>\n      </view>\n    </view>\n    <view class=\"btn\" @click=\"SaveAddress\">保存</view>\n    <view class=\"btnD\" @click=\"show = true\">删除</view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      loading: false,\n      showCity: false,\n      content: '确认删除这条地址吗',\n      title: '删除',\n      show: false,\n      id: '',\n      form: {\n        userName: '',\n        mobile: '',\n        houseNumber: '',\n        address: '点击选择服务地址',\n        addressInfo: '',\n        city: '',\n        cityId: [], // Array of city IDs (province, city, area)\n        lng: '',\n        lat: '',\n        userId: '',\n        sex: 1,\n        status: false,\n        areaId: 0, // Added to match server data\n        provinceId: 0, // Added to match server data\n        cityIds: null, // Added to match server data\n        createTime: 0, // Added to match server data\n        top: 0, // Added to match server data\n        uniacid: 666, // Added to match server data (hardcoded as per example)\n      },\n      columnsCity: [[], [], []],\n    };\n  },\n  methods: {\n    goMap() {\n      // Handle location selection based on platform\n      // #ifdef MP-WEIXIN\n      uni.authorize({\n        scope: 'scope.userLocation',\n        success: () => {\n          uni.chooseLocation({\n            success: (res) => {\n              this.form.address = res.name || '点击选择服务地址';\n              this.form.addressInfo = res.address || '';\n              this.form.lng = res.longitude || '';\n              this.form.lat = res.latitude || '';\n            },\n            fail: (err) => {\n              console.error('Location selection failed:', err);\n              uni.showToast({\n                icon: 'none',\n                title: '选择位置失败',\n                duration: 1500,\n              });\n            },\n          });\n        },\n        fail: (err) => {\n          console.error('Location authorization failed:', err);\n          uni.showToast({\n            icon: 'none',\n            title: '请授权位置信息',\n            duration: 1500,\n          });\n        },\n      });\n      // #endif\n      // #ifdef APP\n      uni.chooseLocation({\n        success: (res) => {\n          this.form.address = res.name || '点击选择服务地址';\n          this.form.addressInfo = res.address || '';\n          this.form.lng = res.longitude || '';\n          this.form.lat = res.latitude || '';\n        },\n        fail: (err) => {\n          console.error('Location selection failed:', err);\n          uni.showToast({\n            icon: 'none',\n            title: '选择位置失败',\n            duration: 1500,\n          });\n        },\n      });\n      // #endif\n    },\n    getcity(parentId) {\n      this.loading = true;\n      this.$api.service\n        .getCity(parentId)\n        .then((res) => {\n          this.columnsCity[0] = res.map((item) => ({\n            id: item.id,\n            title: item.name || item.title,\n          }));\n          return this.$api.service.getCity(res[0]?.id || 0);\n        })\n        .then((res1) => {\n          this.columnsCity[1] = res1.map((item) => ({\n            id: item.id,\n            title: item.name || item.title,\n          }));\n          return this.$api.service.getCity(res1[0]?.id || 0);\n        })\n        .then((res2) => {\n          this.columnsCity[2] = res2.map((item) => ({\n            id: item.id,\n            title: item.name || item.title,\n          }));\n          this.loading = false;\n        })\n        .catch((err) => {\n          console.error('Error fetching city data:', err);\n          this.loading = false;\n          uni.showToast({\n            icon: 'none',\n            title: '无法加载城市数据',\n            duration: 1500,\n          });\n        });\n    },\n    changeHandler(e) {\n      const { columnIndex, index, picker = this.$refs.uPicker } = e;\n      if (columnIndex === 0) {\n        this.$api.service.getCity(this.columnsCity[0][index].id).then((res) => {\n          const newData = res.map((item) => ({\n            id: item.id,\n            title: item.name || item.title,\n          }));\n          picker.setColumnValues(1, newData);\n          this.columnsCity[1] = newData;\n          this.$api.service.getCity(res[0]?.id || 0).then((res1) => {\n            const newData2 = res1.map((item) => ({\n              id: item.id,\n              title: item.name || item.title,\n            }));\n            picker.setColumnValues(2, newData2);\n            this.columnsCity[2] = newData2;\n          });\n        });\n      } else if (columnIndex === 1) {\n        this.$api.service.getCity(this.columnsCity[1][index].id).then((res) => {\n          const newData = res.map((item) => ({\n            id: item.id,\n            title: item.name || item.title,\n          }));\n          picker.setColumnValues(2, newData);\n          this.columnsCity[2] = newData;\n        });\n      }\n    },\n    confirmCity(Array) {\n      // Set city string (e.g., \"安徽-阜阳-临泉\")\n      this.form.city = Array.value\n        .map((item, index) => {\n          if (!item) {\n            return this.columnsCity[index][0]?.title || '';\n          }\n          return item.title;\n        })\n        .filter(Boolean)\n        .join('-');\n\n      // Set cityId array and individual IDs (provinceId, cityId, areaId)\n      const selectedIds = Array.value.map((e, j) => {\n        if (!e) {\n          return this.columnsCity[j][0]?.id || 0;\n        }\n        return e.id;\n      });\n      this.form.cityId = selectedIds;\n      this.form.provinceId = selectedIds[0] || 0;\n      this.form.cityId = selectedIds[1] || 0;\n      this.form.areaId = selectedIds[2] || 0;\n\n      this.showCity = false;\n    },\n    async DelAddress() {\n      try {\n        await this.$api.mine.addressDel(this.id);\n        uni.showToast({\n          icon: 'success',\n          title: '删除成功',\n          duration: 1000,\n        });\n        setTimeout(() => {\n          uni.navigateBack({ delta: 1 });\n        }, 1000);\n      } catch (err) {\n        console.error('Error deleting address:', err);\n        uni.showToast({\n          icon: 'none',\n          title: '删除失败',\n          duration: 1500,\n        });\n      } finally {\n        this.show = false;\n      }\n    },\n    async SaveAddress() {\n      // Validate form fields\n      if (this.form.address === '点击选择服务地址') {\n        uni.showToast({\n          icon: 'none',\n          title: '请选择服务地址',\n          duration: 1500,\n        });\n        return;\n      }\n      const phoneReg = /^1[3456789]\\d{9}$/;\n      if (!phoneReg.test(this.form.mobile)) {\n        uni.showToast({\n          icon: 'none',\n          title: '请填写正确的手机号',\n          duration: 1500,\n        });\n        return;\n      }\n      const requiredFields = ['userName', 'mobile', 'houseNumber', 'city'];\n      for (let key of requiredFields) {\n        if (!this.form[key]) {\n          uni.showToast({\n            icon: 'none',\n            title: '请填写完整信息',\n            duration: 1500,\n          });\n          return;\n        }\n      }\n\n      // Prepare form data for submission\n      let subForm = {\n        id: this.id || 0, // Include ID for updates or 0 for new\n        userName: this.form.userName,\n        mobile: this.form.mobile,\n        houseNumber: this.form.houseNumber,\n        address: this.form.address,\n        addressInfo: this.form.addressInfo,\n        city: this.form.city,\n        cityId: this.form.cityId, // Use the area ID as cityId (based on server data)\n        provinceId: this.form.provinceId || 0,\n        areaId: this.form.areaId || 0,\n        cityIds: this.form.cityId.length ? this.form.cityId.join(',') : null, // Convert array to comma-separated string\n        lng: this.form.lng,\n        lat: this.form.lat,\n        userId: this.form.userId || 0,\n        sex: this.form.sex,\n        status: this.form.status ? 1 : 0,\n        createTime: this.form.createTime || Math.floor(Date.now() / 1000), // Use current timestamp if not set\n        top: this.form.top || 0,\n        uniacid: this.form.uniacid || 666, // Hardcoded as per example\n      };\n\n      console.log('Submitting form:', subForm);\n\n      try {\n        await this.$api.mine.addressUpdate(subForm);\n        uni.showToast({\n          icon: 'success',\n          title: '保存成功',\n          duration: 1000,\n        });\n        setTimeout(() => {\n          uni.navigateBack();\n        }, 1000);\n      } catch (err) {\n        console.error('Error saving address:', err);\n        uni.showToast({\n          icon: 'none',\n          title: '保存失败',\n          duration: 1500,\n        });\n      }\n    },\n  },\n  onLoad(options) {\n    this.id = options.id || '';\n    const editAdress = uni.getStorageSync('editAdress');\n    if (editAdress) {\n\t\tconsole.log(editAdress)\n      // Populate form with editAdress data\n      this.form = {\n        ...this.form,\n        houseNumber: editAdress.houseNumber || '',\n        userName: editAdress.userName || '',\n        mobile: editAdress.mobile || '',\n        sex: editAdress.sex || 1,\n        address: editAdress.address || '点击选择服务地址',\n        addressInfo: editAdress.addressInfo || '',\n        city: editAdress.city || '',\n        cityId: editAdress.cityId || (editAdress.cityIds ? editAdress.cityIds.split(',').map(Number) : []),\n        lng: editAdress.lng || '',\n        lat: editAdress.lat || '',\n        userId: editAdress.userId || '',\n        status: !!editAdress.status,\n        areaId: editAdress.areaId || 0,\n        provinceId: editAdress.provinceId || 0,\n        cityIds: editAdress.cityIds || null,\n        createTime: editAdress.createTime || 0,\n        top: editAdress.top || 0,\n        uniacid: editAdress.uniacid || 666,\n      };\n    }\n    this.getcity(0); // Load city data\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  height: 100vh;\n  background-color: #fff;\n\n  .top {\n    width: 750rpx;\n    height: 58rpx;\n    background: #fff7f1;\n    font-size: 28rpx;\n    font-weight: 400;\n    color: #fe921b;\n    line-height: 58rpx;\n    text-align: center;\n  }\n  .btn {\n    margin: 0 auto;\n    margin-top: 88rpx;\n    width: 690rpx;\n    height: 98rpx;\n    background: #2e80fe;\n    border-radius: 50rpx;\n    font-size: 32rpx;\n    font-weight: 500;\n    color: #ffffff;\n    line-height: 98rpx;\n    text-align: center;\n  }\n  .btnD {\n    margin: 0 auto;\n    margin-top: 40rpx;\n    width: 690rpx;\n    height: 98rpx;\n    border-radius: 50rpx;\n    font-size: 32rpx;\n    font-weight: 500;\n    color: #2e80fe;\n    line-height: 98rpx;\n    text-align: center;\n    border: 2rpx solid #2e80fe;\n  }\n\n  .main {\n    padding: 0 30rpx;\n\n    .main_item {\n      padding: 40rpx 0;\n      border-bottom: 2rpx solid #e9e9e9;\n      display: flex;\n      align-items: center;\n      position: relative;\n\n      .name {\n        width: 112rpx;\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #333333;\n        margin-right: 40rpx;\n      }\n\n      .address {\n        font-size: 28rpx;\n        font-weight: 400;\n        color: #adadad;\n      }\n\n      image {\n        width: 23rpx;\n        height: 27rpx;\n        position: absolute;\n        right: 0;\n        top: 46rpx;\n      }\n      input {\n        width: 450rpx;\n        font-size: 28rpx;\n        color: #333333;\n      }\n      .box {\n        display: flex;\n        align-items: center;\n        .box_item {\n          margin-right: 20rpx;\n          width: 88rpx;\n          height: 50rpx;\n          background: #ffffff;\n          border-radius: 4rpx;\n          border: 2rpx solid #ededed;\n          font-size: 28rpx;\n          font-weight: 400;\n          color: #adadad;\n          line-height: 46rpx;\n          text-align: center;\n        }\n      }\n    }\n    .last {\n      justify-content: space-between;\n      .name {\n        width: 170rpx;\n      }\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit_address.vue?vue&type=style&index=0&id=a5731d36&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./edit_address.vue?vue&type=style&index=0&id=a5731d36&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755411526375\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}